<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原子模型可视化</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 2px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        button.active {
            background: linear-gradient(45deg, #f093fb, #f5576c);
        }
        
        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 8px;
            color: #333;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="controls">
            <div class="control-group">
                <label>选择元素:</label>
                <button id="hydrogen" class="active">氢 (H)</button>
                <button id="helium">氦 (He)</button>
            </div>
        </div>
        
        <div id="info">
            <div id="element-info">氢原子 - 1个质子，1个电子</div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        /**
         * 原子模型可视化类
         * 实现玻尔模型的原子结构展示
         */
        class AtomModel {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.nucleus = null;
                this.electrons = [];
                this.orbits = [];
                this.currentElement = 'hydrogen';
                this.animationId = null;
                
                this.init();
                this.setupControls();
                this.animate();
            }
            
            /**
             * 初始化Three.js场景
             */
            init() {
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x000011);
                
                // 创建相机
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(0, 0, 15);
                
                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.getElementById('container').appendChild(this.renderer.domElement);
                
                // 添加光照
                this.setupLighting();
                
                // 创建原子模型
                this.createAtom();
                
                // 添加鼠标控制
                this.setupMouseControls();
                
                // 监听窗口大小变化
                window.addEventListener('resize', () => this.onWindowResize());
            }
            
            /**
             * 设置场景光照
             */
            setupLighting() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                this.scene.add(ambientLight);
                
                // 点光源
                const pointLight = new THREE.PointLight(0xffffff, 1, 100);
                pointLight.position.set(10, 10, 10);
                pointLight.castShadow = true;
                this.scene.add(pointLight);
                
                // 方向光
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
                directionalLight.position.set(-1, 1, 1);
                this.scene.add(directionalLight);
            }
            
            /**
             * 创建原子结构
             */
            createAtom() {
                this.clearAtom();
                
                // 创建原子核
                this.createNucleus();
                
                // 根据元素类型创建电子轨道
                if (this.currentElement === 'hydrogen') {
                    this.createHydrogenAtom();
                } else if (this.currentElement === 'helium') {
                    this.createHeliumAtom();
                }
            }
            
            /**
             * 创建原子核
             */
            createNucleus() {
                const nucleusGeometry = new THREE.SphereGeometry(0.5, 32, 32);
                const nucleusMaterial = new THREE.MeshPhongMaterial({
                    color: 0xff4444,
                    shininess: 100,
                    transparent: true,
                    opacity: 0.9
                });
                
                this.nucleus = new THREE.Mesh(nucleusGeometry, nucleusMaterial);
                this.nucleus.castShadow = true;
                this.nucleus.receiveShadow = true;
                this.scene.add(this.nucleus);
                
                // 添加原子核发光效果
                const glowGeometry = new THREE.SphereGeometry(0.7, 16, 16);
                const glowMaterial = new THREE.MeshBasicMaterial({
                    color: 0xff6666,
                    transparent: true,
                    opacity: 0.3
                });
                const glow = new THREE.Mesh(glowGeometry, glowMaterial);
                this.nucleus.add(glow);
            }
            
            /**
             * 创建氢原子（1个电子）
             */
            createHydrogenAtom() {
                this.createElectronOrbit(3, 0, 1);
                this.updateElementInfo('氢原子 - 1个质子，1个电子');
            }
            
            /**
             * 创建氦原子（2个电子）
             */
            createHeliumAtom() {
                this.createElectronOrbit(3, 0, 1);
                this.createElectronOrbit(3, Math.PI, 1);
                this.updateElementInfo('氦原子 - 2个质子，2个电子');
            }
            
            /**
             * 创建电子轨道和电子
             * @param {number} radius - 轨道半径
             * @param {number} phase - 初始相位
             * @param {number} speed - 运动速度
             */
            createElectronOrbit(radius, phase, speed) {
                // 创建轨道线
                const orbitGeometry = new THREE.RingGeometry(radius - 0.05, radius + 0.05, 64);
                const orbitMaterial = new THREE.MeshBasicMaterial({
                    color: 0x666666,
                    transparent: true,
                    opacity: 0.3,
                    side: THREE.DoubleSide
                });
                const orbit = new THREE.Mesh(orbitGeometry, orbitMaterial);
                orbit.rotation.x = Math.PI / 2;
                this.scene.add(orbit);
                this.orbits.push(orbit);
                
                // 创建电子
                const electronGeometry = new THREE.SphereGeometry(0.15, 16, 16);
                const electronMaterial = new THREE.MeshPhongMaterial({
                    color: 0x4444ff,
                    shininess: 100,
                    transparent: true,
                    opacity: 0.9
                });
                const electron = new THREE.Mesh(electronGeometry, electronMaterial);
                electron.castShadow = true;
                
                // 添加电子发光效果
                const electronGlowGeometry = new THREE.SphereGeometry(0.25, 8, 8);
                const electronGlowMaterial = new THREE.MeshBasicMaterial({
                    color: 0x6666ff,
                    transparent: true,
                    opacity: 0.4
                });
                const electronGlow = new THREE.Mesh(electronGlowGeometry, electronGlowMaterial);
                electron.add(electronGlow);
                
                // 设置电子运动参数
                electron.userData = {
                    radius: radius,
                    phase: phase,
                    speed: speed,
                    angle: phase
                };
                
                this.scene.add(electron);
                this.electrons.push(electron);
            }
            
            /**
             * 清除当前原子模型
             */
            clearAtom() {
                // 清除原子核
                if (this.nucleus) {
                    this.scene.remove(this.nucleus);
                    this.nucleus = null;
                }
                
                // 清除电子
                this.electrons.forEach(electron => {
                    this.scene.remove(electron);
                });
                this.electrons = [];
                
                // 清除轨道
                this.orbits.forEach(orbit => {
                    this.scene.remove(orbit);
                });
                this.orbits = [];
            }
            
            /**
             * 设置鼠标控制
             */
            setupMouseControls() {
                let isMouseDown = false;
                let mouseX = 0;
                let mouseY = 0;
                let targetRotationX = 0;
                let targetRotationY = 0;
                let currentRotationX = 0;
                let currentRotationY = 0;
                
                this.renderer.domElement.addEventListener('mousedown', (event) => {
                    isMouseDown = true;
                    mouseX = event.clientX;
                    mouseY = event.clientY;
                });
                
                this.renderer.domElement.addEventListener('mousemove', (event) => {
                    if (isMouseDown) {
                        const deltaX = event.clientX - mouseX;
                        const deltaY = event.clientY - mouseY;
                        
                        targetRotationY += deltaX * 0.01;
                        targetRotationX += deltaY * 0.01;
                        
                        mouseX = event.clientX;
                        mouseY = event.clientY;
                    }
                });
                
                this.renderer.domElement.addEventListener('mouseup', () => {
                    isMouseDown = false;
                });
                
                // 平滑旋转更新
                const updateRotation = () => {
                    currentRotationX += (targetRotationX - currentRotationX) * 0.1;
                    currentRotationY += (targetRotationY - currentRotationY) * 0.1;
                    
                    this.scene.rotation.x = currentRotationX;
                    this.scene.rotation.y = currentRotationY;
                    
                    requestAnimationFrame(updateRotation);
                };
                updateRotation();
            }
            
            /**
             * 设置控制按钮
             */
            setupControls() {
                const hydrogenBtn = document.getElementById('hydrogen');
                const heliumBtn = document.getElementById('helium');
                
                hydrogenBtn.addEventListener('click', () => {
                    this.switchElement('hydrogen');
                    hydrogenBtn.classList.add('active');
                    heliumBtn.classList.remove('active');
                });
                
                heliumBtn.addEventListener('click', () => {
                    this.switchElement('helium');
                    heliumBtn.classList.add('active');
                    hydrogenBtn.classList.remove('active');
                });
            }
            
            /**
             * 切换元素
             * @param {string} element - 元素类型
             */
            switchElement(element) {
                this.currentElement = element;
                this.createAtom();
            }
            
            /**
             * 更新元素信息显示
             * @param {string} info - 信息文本
             */
            updateElementInfo(info) {
                document.getElementById('element-info').textContent = info;
            }
            
            /**
             * 窗口大小变化处理
             */
            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }
            
            /**
             * 动画循环
             */
            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());
                
                // 更新电子位置
                this.electrons.forEach(electron => {
                    const userData = electron.userData;
                    userData.angle += userData.speed * 0.02;
                    
                    electron.position.x = Math.cos(userData.angle) * userData.radius;
                    electron.position.z = Math.sin(userData.angle) * userData.radius;
                    
                    // 电子自转
                    electron.rotation.y += 0.1;
                });
                
                // 原子核缓慢自转
                if (this.nucleus) {
                    this.nucleus.rotation.y += 0.005;
                    this.nucleus.rotation.x += 0.002;
                }
                
                // 轨道微弱闪烁效果
                this.orbits.forEach((orbit, index) => {
                    const time = Date.now() * 0.001;
                    orbit.material.opacity = 0.2 + 0.1 * Math.sin(time + index);
                });
                
                this.renderer.render(this.scene, this.camera);
            }
            
            /**
             * 销毁实例
             */
            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }
                this.clearAtom();
                if (this.renderer) {
                    this.renderer.dispose();
                }
            }
        }
        
        // 初始化应用
        const atomModel = new AtomModel();
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            atomModel.destroy();
        });
    </script>
</body>
</html>